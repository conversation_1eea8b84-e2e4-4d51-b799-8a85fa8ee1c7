#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <float.h>
// 二维点
typedef struct {
    double x, y;
} Point;

// BST节点
typedef struct Node {
    Point pt;           // 存储的点
    struct Node *lft;   // 左子树
    struct Node *rgt;   // 右子树
} Node;

// 计算两点间的距离
double calc_dist(Point p1, Point p2) {
    double dx, dy;
    dx = p1.x - p2.x;
    dy = p1.y - p2.y;
    return sqrt(dx * dx + dy * dy);
}

// 创建新节点
Node* create_node(Point pt) {
    Node *nd;
    nd = (Node*)malloc(sizeof(Node));
    if (nd == NULL) {
        printf("内存分配失败了，程序要挂了\n");
        exit(1);
    }
    nd->pt = pt;
    nd->lft = NULL;
    nd->rgt = NULL;
    return nd;
}

// 向BST插入新点，按x坐标排序
Node* insert(Node *root, Point pt) {
    // 如果是空树，直接创建根节点
    if (root == NULL) {
        return create_node(pt);
    }
    // 按x坐标比较，决定插入左边还是右边
    if (pt.x < root->pt.x) {
        root->lft = insert(root->lft, pt);
    } else {
        root->rgt = insert(root->rgt, pt);
    }
    
    return root;
}

// 最近邻搜索核心函数，递归查找
void find_lin(Node *root, Point query, Node **best, double *min_dst) {
    double cur_dst, dx;
    // 空节点不用找
    if (root == NULL) return;
    // 计算当前节点到查询点的距离
    cur_dst = calc_dist(root->pt, query);
    // 如果找到更近的点，就更新最佳结果
    if (cur_dst < *min_dst) {
        *min_dst = cur_dst;
        *best = root;
    }
    // 计算x方向的距离
    dx = query.x - root->pt.x;
    // 先搜索更可能包含最近点的一边
    if (dx < 0) {
        // 查询点在左边，先搜左子树
        find_lin(root->lft, query, best, min_dst);
        // 如果右边可能有更近的点，也要搜一下
        if (fabs(dx) < *min_dst) {
            find_lin(root->rgt, query, best, min_dst);
        }
    } else {
        // 查询点在右边，先搜右子树
        find_lin(root->rgt, query, best, min_dst);
        // 如果左边可能有更近的点，也要搜一下
        if (fabs(dx) < *min_dst) {
            find_lin(root->lft, query, best, min_dst);
        }
    }
}

// 对外接口，查找最近邻点
Node* nergnbor(Node *root, Point query) {
    Node *best;
    double min_dst;
    if (root == NULL) {
        printf("树是空的，找不到任何点\n");
        return NULL;
    }
    best = root;
    min_dst = DBL_MAX;  // 初始化为最大值
    find_lin(root, query, &best, &min_dst);
    return best;
}

// 打印点的坐标
void show_point(Point pt) {
    printf("(%.2f, %.2f)", pt.x, pt.y);
}

// 找到最小值节点，用于删除操作
Node* find_min(Node *root) {
    while (root && root->lft) {
        root = root->lft;
    }
    return root;
}

// 删除指定点，按x坐标匹配
Node* del_point(Node *root, Point pt) {
    Node *temp;
    if (root == NULL) return NULL;
    if (pt.x < root->pt.x) {
        root->lft = del_point(root->lft, pt);
    } else if (pt.x > root->pt.x) {
        root->rgt = del_point(root->rgt, pt);
    } else {
        // 找到要删除的节点了
        if (fabs(root->pt.y - pt.y) < 0.001) {  // y坐标也要匹配
            if (root->lft == NULL) {
                temp = root->rgt;
                free(root);
                return temp;
            } else if (root->rgt == NULL) {
                temp = root->lft;
                free(root);
                return temp;
            }

            // 有两个子节点的情况
            temp = find_min(root->rgt);
            root->pt = temp->pt;
            root->rgt = del_point(root->rgt, temp->pt);
        }
    }
    return root;
}

// 中序遍历打印所有点
void showall(Node *root) {
    if (root == NULL) return;
    showall(root->lft);
    printf("  ");
    show_point(root->pt);
    printf("\n");
    showall(root->rgt);
}

// 统计BST中点的数量
int count_points(Node *root) {
    if (root == NULL) return 0;
    return 1 + count_points(root->lft) + count_points(root->rgt);
}

// 释放BST内存
void free_tree(Node *root) {
    if (root == NULL) return;
    free_tree(root->lft);
    free_tree(root->rgt);
    free(root);
}


// 插入点
Node* insert_view(Node *root) {
    Point new_pt;

    printf("输入一下要插入的点坐标 (x y): ");
    if (scanf("%lf %lf", &new_pt.x, &new_pt.y) != 2) {
        printf("输入格式不对，插入失败\n");
        return root;
    }

    root = insert(root, new_pt);
    printf("成功插入点 ");
    show_point(new_pt);
    printf("\n");

    return root;
}

// 删除点
Node* delview(Node *root) {
    Point del_pt;
    int count_before, count_after;

    if (root == NULL) {
        printf("树是空的，没有点可以删除\n");
        return root;
    }
    printf("输入一下要删除的点坐标 (x y): ");
    if (scanf("%lf %lf", &del_pt.x, &del_pt.y) != 2) {
        printf("输入格式不对，删除失败\n");
        return root;
    }
    count_before = count_points(root);
    root = del_point(root, del_pt);
    count_after = count_points(root);
    if (count_after < count_before) {
        printf("成功删除点 ");
        show_point(del_pt);
        printf("\n");
    } else {
        printf("没找到指定的点，删除失败\n");
    }
    return root;
}

// 查看所有点
void show(Node *root) {
    int count;
    count = count_points(root);
    if (count == 0) {
        printf("树是空的，没有任何点\n");
        return;
    }
    printf("当前共有 %d 个点:\n", count);
    showall(root);
}

// 查询最近邻
void query_view(Node *root) {
    Point query;
    Node *nearest;

    if (root == NULL) {
        printf("树是空的，无法进行查询\n");
        return;
    }
    printf("输入一下要查询点坐标 (x y): ");
    if (scanf("%lf %lf", &query.x, &query.y) != 2) {
        printf("输入格式不对，查询失败\n");
        return;
    }
    printf("查询点: ");
    show_point(query);
    printf("\n");
    nearest = nergnbor(root, query);
    if (nearest != NULL) {
        printf("找到了最近的点: ");
        show_point(nearest->pt);
        printf("，距离为: %.2f\n", calc_dist(query, nearest->pt));
    } else {
        printf("没找到任何点\n");
    }
}

int main() {
    Node *root;
    int choice;
    root = NULL;
    while (1) {
        printf("\n==========================\n");
        printf("     BST最近邻搜索系统    \n");
        printf("==========================\n");
        printf("1. 插入测试点\n");
        printf("2. 删除测试点\n");
        printf("3. 查看所有点\n");
        printf("4. 查询最近邻\n");
        printf("5. 退出程序\n");
        printf("==========================\n");
        printf("选择操作 (1-5): ");
        if (scanf("%d", &choice) != 1) {
            printf("输入无效，请输入数字\n");
            while (getchar() != '\n');
            continue;
        }
        switch (choice) {
            case 1:
                root = insert_view(root);
                break;
            case 2:
                root = delview(root);
                break;
            case 3:
                show(root);
                break;
            case 4:
                query_view(root);
                break;
            case 5:
                printf("感谢使用，再见！\n");
                free_tree(root);
                return 0;
            default:
                printf("无效选择，请输入1-5之间的数字\n");
                break;
        }
    }
}
