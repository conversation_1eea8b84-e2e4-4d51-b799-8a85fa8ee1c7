# 基于二叉搜索树的二维平面最近邻搜索系统课程设计报告

## 1. 课程设计概述

### 1.1 问题描述

这次课程设计主要解决的是二维平面上的最近邻搜索问题。在实际应用中，我们经常需要在一堆散布在平面上的点中快速找到离某个查询点最近的那个点，比如在地图应用中找最近的加油站，或者在图像处理中找最相似的特征点。传统的暴力搜索方法需要遍历所有点来计算距离，时间复杂度是O(n)，当数据量很大的时候效率就比较低了。

我选择用二叉搜索树来组织这些二维点，通过树的层次结构来加速搜索过程。虽然BST在最坏情况下可能退化成链表，但在平均情况下能够提供不错的搜索性能，而且实现起来相对简单，很适合作为课程设计来理解数据结构的应用。

### 1.2 基本要求

这个系统需要完成以下几个核心功能：

(1) 要求能够动态插入二维平面上的点，并且保持BST的有序性质，我这里选择按x坐标作为排序的依据。

(2) 实现高效的最近邻搜索算法，能够在给定查询点的情况下快速找到距离最近的已存储点。

(3) 实现点的删除功能，在删除节点时要保持BST的结构完整性，特别是处理有两个子节点的情况。

(4) 实现对所有存储点的遍历和显示功能，让用户能够查看当前系统中存储的所有点。

(5) 系统界面友好，风格统一易操作。我设计了一个简洁的菜单系统，用户通过数字选择就能完成各种操作。

(6) 系统运行稳定，可以完成异常数据的检测和处理，运行结果正确可靠。我在代码中加入了输入验证和错误处理机制。

(7) 系统有较好的时间和空间效率。BST的平均搜索时间复杂度是O(log n)，空间复杂度是O(n)。

(8) 文档结构合理，格式规范，图表清晰。

### 1.3 开发环境

(1) 操作系统：Windows 11

(2) 编程语言：C语言

(3) 编译器：GCC编译器

(4) 开发工具：Visual Studio Code

## 2. 数据结构及算法分析

### 2.1 数据结构分析

我在这个系统中主要采用了两种数据结构：Point结构体和BST节点结构体。

Point结构体很简单，就是用两个double类型的变量x和y来表示二维平面上的一个点。选择double而不是int是因为实际应用中坐标往往不是整数，需要支持小数点后的精度。

BST节点结构体是整个系统的核心，每个节点包含一个Point类型的数据域pt，以及指向左右子树的指针lft和rgt。我选择用BST来组织这些点主要是因为它能够提供相对高效的搜索性能。虽然对于二维数据来说，k-d树可能是更好的选择，但BST实现起来更简单，而且在数据分布比较均匀的情况下性能也不错。

在BST的构建过程中，我选择按x坐标作为比较的依据，这样可以保证树的有序性。当然这种方法有个缺点，就是如果所有点的x坐标都差不多，树就会变得很不平衡，但对于一般的应用场景来说还是够用的。

### 2.2 算法分析

最近邻搜索算法是这个系统的核心，我采用的是基于BST的递归搜索方法。算法的基本思想是利用BST的有序性质来剪枝，避免搜索那些不可能包含最近邻的子树。

具体来说，算法首先计算当前节点到查询点的距离，如果比目前找到的最小距离还小，就更新最佳结果。然后根据查询点和当前节点在x方向上的位置关系，优先搜索更可能包含最近邻的那一边。关键的优化在于，只有当另一边可能包含更近的点时（即x方向的距离小于当前最小距离），才会去搜索另一边的子树。

这种剪枝策略大大减少了需要访问的节点数量，在理想情况下时间复杂度可以达到O(log n)。当然，在最坏情况下（比如树完全不平衡），时间复杂度会退化到O(n)，但这种情况在实际应用中比较少见。

## 3. 详细设计与实现

### 3.1 总体结构

整个系统包括五个主要模块，每个模块负责不同的功能，通过主函数的菜单系统来协调工作。

(1) 数据管理模块：该模块负责BST的基本操作，包括节点的创建、插入、删除和内存管理。

(2) 搜索算法模块：该模块实现最近邻搜索的核心算法，包括距离计算和递归搜索逻辑。

(3) 用户交互模块：该模块处理用户的输入输出，提供友好的操作界面和错误提示。

(4) 数据展示模块：该模块负责遍历和显示BST中的所有点，以及统计点的数量。

(5) 主控制模块：该模块通过菜单系统协调各个功能模块的工作，处理用户的选择和程序的流程控制。

### 3.2 模块设计

#### 3.2.1 数据管理模块

**节点创建函数 (create_node)**
```
输入：Point类型的点坐标
输出：指向新创建节点的指针
功能：动态分配内存创建新的BST节点
算法描述：
1. 分配Node结构体大小的内存空间
2. 检查内存分配是否成功，失败则退出程序
3. 初始化节点的点坐标和左右子树指针
4. 返回新节点的指针
```

这个函数比较简单，主要就是做内存分配和初始化工作。我在这里加了内存分配失败的检查，虽然在现代计算机上很少会遇到内存不足的情况，但作为一个健壮的程序还是应该考虑这种异常情况。

**插入函数 (insert)**
```
输入：BST根节点指针，要插入的点坐标
输出：更新后的BST根节点指针
功能：按x坐标大小将新点插入到BST中
算法描述：
1. 如果当前节点为空，创建新节点作为根节点
2. 比较新点与当前节点的x坐标
3. 如果新点x坐标较小，递归插入到左子树
4. 否则递归插入到右子树
5. 返回当前节点指针
```

插入算法采用的是标准的BST插入方法，通过递归的方式找到合适的插入位置。我选择按x坐标作为比较依据，这样可以保证BST的有序性质。虽然这种方法在处理x坐标相同的点时可能会有问题，但在实际应用中这种情况比较少见。

**删除函数 (del_point)**
```
输入：BST根节点指针，要删除的点坐标
输出：更新后的BST根节点指针
功能：从BST中删除指定的点
算法描述：
1. 如果当前节点为空，返回NULL
2. 根据x坐标比较，递归查找要删除的节点
3. 找到目标节点后，根据子节点情况分类处理：
   - 无子节点：直接删除
   - 有一个子节点：用子节点替换当前节点
   - 有两个子节点：用右子树的最小节点替换当前节点
4. 释放被删除节点的内存
5. 返回更新后的节点指针
```

删除操作是BST中最复杂的操作，特别是处理有两个子节点的情况。我采用的是用右子树最小节点替换被删除节点的方法，这样可以保持BST的有序性质。在匹配要删除的点时，我不仅比较x坐标，还要比较y坐标，确保删除的是完全匹配的点。

#### 3.2.2 搜索算法模块

**距离计算函数 (calc_dist)**
```
输入：两个Point类型的点
输出：两点间的欧几里得距离
功能：计算二维平面上两点间的直线距离
算法描述：
1. 计算两点在x方向的距离差dx
2. 计算两点在y方向的距离差dy
3. 使用勾股定理计算欧几里得距离：sqrt(dx² + dy²)
4. 返回计算结果
```

这个函数实现的是标准的欧几里得距离公式，在二维平面上这就是两点间的直线距离。我使用了math.h库中的sqrt函数来计算平方根，这样计算出来的距离是精确的浮点数。

**最近邻搜索函数 (find_lin)**
```
输入：BST根节点，查询点，最佳结果指针，最小距离指针
输出：通过指针参数返回最近邻节点和距离
功能：递归搜索BST找到距离查询点最近的节点
算法描述：
1. 如果当前节点为空，直接返回
2. 计算当前节点到查询点的距离
3. 如果距离比当前最小距离更小，更新最佳结果
4. 计算查询点与当前节点在x方向的距离差
5. 根据x方向位置关系，优先搜索更可能的一边
6. 如果另一边可能包含更近的点，也进行搜索
7. 递归处理子树
```

这是整个系统最核心的算法，通过递归搜索和剪枝优化来提高搜索效率。算法的关键在于第6步的剪枝判断：只有当x方向的距离小于当前最小距离时，才有必要搜索另一边的子树，这样可以避免很多不必要的计算。

#### 3.2.3 用户交互模块

**插入界面函数 (insert_view)**
```
输入：BST根节点指针
输出：更新后的BST根节点指针
功能：处理用户插入点的交互过程
算法描述：
1. 提示用户输入点的坐标
2. 使用scanf读取x和y坐标
3. 检查输入格式是否正确
4. 调用insert函数插入新点
5. 显示插入成功的提示信息
6. 返回更新后的根节点指针
```

这个函数主要负责用户交互，我在这里加入了输入格式检查，如果用户输入的不是两个数字，程序会给出错误提示而不是崩溃。这种错误处理机制让程序更加健壮。

**删除界面函数 (delview)**
```
输入：BST根节点指针
输出：更新后的BST根节点指针
功能：处理用户删除点的交互过程
算法描述：
1. 检查BST是否为空
2. 提示用户输入要删除的点坐标
3. 记录删除前的点数量
4. 调用del_point函数删除指定点
5. 比较删除前后的点数量，判断删除是否成功
6. 显示相应的提示信息
7. 返回更新后的根节点指针
```

删除界面函数通过比较删除前后的点数量来判断删除操作是否成功，这是一个比较巧妙的方法。如果用户输入的点在BST中不存在，删除操作不会改变点的数量，程序就能检测到这种情况并给出相应提示。

**查询界面函数 (query_view)**
```
输入：BST根节点指针
输出：无（直接显示查询结果）
功能：处理用户最近邻查询的交互过程
算法描述：
1. 检查BST是否为空
2. 提示用户输入查询点坐标
3. 检查输入格式是否正确
4. 调用nergnbor函数查找最近邻
5. 显示查询点和最近邻点的信息
6. 计算并显示两点间的距离
```

查询界面是用户最常用的功能，我在设计时特别注意了信息的完整性，不仅显示最近邻点的坐标，还会显示查询点的坐标和两点间的距离，让用户能够清楚地了解查询结果。

#### 3.2.4 数据展示模块

**遍历显示函数 (showall)**
```
输入：BST根节点指针
输出：无（直接打印所有点）
功能：中序遍历BST并显示所有点的坐标
算法描述：
1. 如果当前节点为空，直接返回
2. 递归遍历左子树
3. 显示当前节点的点坐标
4. 递归遍历右子树
```

我选择用中序遍历来显示所有点，这样显示出来的点会按x坐标从小到大排序，看起来比较有规律。虽然对于二维点来说这种排序不一定有实际意义，但至少让输出看起来更整齐。

**点数统计函数 (count_points)**
```
输入：BST根节点指针
输出：BST中点的总数量
功能：递归统计BST中节点的数量
算法描述：
1. 如果当前节点为空，返回0
2. 递归统计左子树的节点数量
3. 递归统计右子树的节点数量
4. 返回 1 + 左子树节点数 + 右子树节点数
```

这是一个标准的树节点计数算法，通过递归的方式统计整棵树的节点数量。我在删除操作中用这个函数来判断删除是否成功，在显示所有点时用它来显示总数量。

## 4. 测试与结果分析

为了验证系统的正确性和性能，我进行了多组测试，包括功能测试和性能测试。

### 4.1 功能测试

我首先测试了系统的基本功能，包括插入、删除、查询和显示操作。测试过程中插入了多个测试点：(1.0, 2.0)、(3.0, 4.0)、(5.0, 1.0)、(2.0, 3.0)、(4.0, 5.0)等，系统都能正确处理。

在最近邻查询测试中，我用查询点(2.5, 2.5)进行测试，系统正确找到了最近的点(2.0, 3.0)，距离为0.71。这个结果通过手工计算验证是正确的。我还测试了边界情况，比如查询点就是BST中的某个点，系统也能正确返回距离为0的结果。

删除功能的测试包括了三种情况：删除叶子节点、删除只有一个子节点的节点、删除有两个子节点的节点。系统在所有情况下都能正确维护BST的结构，删除后的树仍然满足BST的性质。

### 4.2 性能分析

从时间效率来看，插入、删除和搜索操作的平均时间复杂度都是O(log n)，这在理论上是比较理想的。我用不同规模的数据进行了测试，当点的数量从10个增加到1000个时，搜索时间的增长基本符合对数增长的规律。

不过我也注意到，当插入的点在x坐标上比较有序时，BST会变得不平衡，搜索性能会下降。比如按x坐标递增的顺序插入点时，BST会退化成一个链表，搜索时间复杂度变成O(n)。这是BST的一个固有缺陷，在实际应用中可能需要考虑使用平衡二叉树来解决。

从空间效率来看，每个节点需要存储一个Point结构体和两个指针，在64位系统上大约需要32字节。对于n个点，总的空间复杂度是O(n)，这是比较合理的。

### 4.3 错误处理测试

我还专门测试了系统的错误处理能力。当用户输入非数字字符时，系统能够检测到输入错误并给出提示，而不会崩溃。当尝试在空树中进行查询或删除操作时，系统也能给出合适的提示信息。

内存管理方面，我使用了free_tree函数来释放整棵树的内存，避免内存泄漏。虽然在这个简单的程序中内存泄漏不会造成太大问题，但养成良好的内存管理习惯还是很重要的。

## 5. 总结

### 5.1 学习收获

通过这次课程设计，我对数据结构有了更深入的理解，特别是二叉搜索树的实现和应用。以前只是在课本上学习BST的理论知识，这次亲手实现了一个完整的BST应用系统，让我真正体会到了数据结构在解决实际问题中的重要作用。

在编程实践方面，我学会了如何设计一个模块化的程序结构，如何处理用户输入和错误情况，如何进行内存管理等。特别是在实现最近邻搜索算法时，我深刻理解了算法优化的重要性，一个好的剪枝策略能够大大提高搜索效率。

这次设计也让我认识到了理论与实践的差距。虽然BST在理论上有很好的性能，但在实际应用中会遇到各种问题，比如数据分布不均匀导致的树不平衡问题。这提醒我在以后的学习中要更加注重实践，不能只停留在理论层面。

### 5.2 不足与改进

回顾整个设计过程，我发现还有不少可以改进的地方。首先是BST的平衡性问题，当前的实现在面对有序数据时性能会严重下降，如果要在实际项目中使用，应该考虑实现AVL树或红黑树等平衡二叉树。

其次是搜索算法的局限性，目前只按x坐标构建BST，对于二维数据来说不是最优的选择。更好的方法是使用k-d树，它专门为多维数据设计，能够在各个维度上都保持平衡，搜索效率更高。另外，我在实现过程中发现自己对一些细节考虑不够周全，比如浮点数比较时的精度问题，删除操作中的边界情况处理等，这些都需要在以后的编程中更加仔细。
