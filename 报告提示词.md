```
我需要你充分阅读我项目下的代码,根据代码完成一篇课程设计报告,你需要保证文风偏口语化,图表结合,图片使用mermaid完成绘制,每个部分都尽可能的详细一些,尽可能的一部分用一段话描述,而不是过多的分点陈述,你需要尽可能的消除AI写作痕迹,符合一个大学生的阐述角度。
以下是需要提供的报告内容
1.课程设计概述
1.1问题描述（目的，内容，题目等）

就是你做的问题是什么，如果是**问题，请说明**问题是什么问题
1.2基本要求（要完成什么功能，有什么性能，）
(1)要求***
(2)实现**
(3)实现***
(4)实现对……（总之就是你要实现的功能）
(5)系统界面友好，风格统一易操作。
(6)系统运行稳定，可以完成异常数据的检测和处理，运行结果正确可靠
(7)系统有较好的时间和空间效率。
(8)文档结构合理，格式规范，图表清晰
...
1.3开发环境
(1)
(2)
(3)
2.数据结构及算法分析
2.1数据结构分析（采用**等数据结构的原因及具体结构）
如果就用的简单数据类型或者普通数组就说明一句也可。

2.2算法分析（比如采用**法，描述**法的原理以及应用到此处的理由）
如果没用什么算法，可以不写。
3.详细设计与实现
3.1总体结构（文字说明整体包括几个模块，每个模块的功能，然后画总体结构图）：
系统包括***几个模块
(1)***模块：该模块....（一句话）
(2)***模块：该模块....（一句话）
(3)***模块：该模块....（一句话）
...

3.2模块设计
（具体每个模块的设计实现，用伪码描述，包括输入输出，算法功能，以及具体算法描述）

4.测试与结果分析
测试系统，根据实验结果，进行分析。包括实验结果，时间效率和空间效率。
5.2总结
课程设计的学到了什么，有什么不足。用两段话描述

完成后保存到report.md中
```

